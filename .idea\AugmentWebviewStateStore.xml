<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;e45f177e-1552-4edc-9f7d-7c50a482ce1c&quot;,&quot;conversations&quot;:{&quot;e45f177e-1552-4edc-9f7d-7c50a482ce1c&quot;:{&quot;id&quot;:&quot;e45f177e-1552-4edc-9f7d-7c50a482ce1c&quot;,&quot;name&quot;:&quot;Welcome to the Augment Agent&quot;,&quot;createdAtIso&quot;:&quot;2025-06-23T10:37:16.872Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-11T14:02:36.738Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;b234b5c6-b032-4c59-b300-5fb5620b9360&quot;,&quot;uuid&quot;:&quot;26caaa0a-6e6c-413d-93fb-ee3186e1066a&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1750675036872,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;f7fab104-b2fe-440d-8342-f605a50db993&quot;,&quot;request_message&quot;:&quot;帮我简化readme文件，只需要make up就可拉起容器，make down卸载容器，剩下自由发挥&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;帮我简化readme文件，只需要make up就可拉起容器，make down卸载容器，剩下自由发挥&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;帮我简化readme文件，只需要make up就可拉起容器，make down卸载容器，剩下自由发挥&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/AIGC/code/openmemory&quot;,&quot;folderRoot&quot;:&quot;D:/AIGC/code/openmemory&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-11T14:02:36.738Z&quot;,&quot;response_text&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-c38d3079-946a-4a64-bab9-8ba365aba363&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;D:/AIGC/code/openmemoryfalsefalse&quot;,&quot;userGuidelines&quot;,&quot;agentMemories&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;029ed896-fe2e-43eb-b0bb-3ce60924615e&quot;},&quot;c2a4ac90-a85d-40bd-9440-3d68f41ded15&quot;:{&quot;id&quot;:&quot;c2a4ac90-a85d-40bd-9440-3d68f41ded15&quot;,&quot;createdAtIso&quot;:&quot;2025-07-11T14:02:15.826Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-11T14:02:15.826Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;__NEW_AGENT__&quot;:{&quot;id&quot;:&quot;__NEW_AGENT__&quot;,&quot;createdAtIso&quot;:&quot;2025-07-11T14:02:15.864Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-11T14:02:15.885Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>