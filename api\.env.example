# ------ Required Configurations ------
USER=user

# --- Optional Configurations for Categorization Feature ---
# The configuration here will be applied to openmemory\api\app\utils\categorization.py
# Uncomment and set if you have a specific endpoint for categorization.
# CATEGORIZATION_OPENAI_BASE_URL=https://api.openai.com/v1

# Uncomment and set if you have a specific API key for categorization tasks.
# CATEGORIZATION_OPENAI_API_KEY=sk-xxx

# Uncomment and set if you want to use a specific model for categorization.
# CATEGORIZATION_OPENAI_MODEL=gpt-4o-mini


# ------ Optional Configurations for Memory ------
# In openmemory\api\app\utils\memory.py, you'll find the initialization of the LLM and embedder models.
# These are optional configurations.
# "When customizing LLM models, please remember to synchronously modify the OPENAI_API_KEY"

# Uncomment and set if you want to use a specific provider for llm model.
# OPENAI_PROVIDER=openai_structured

# Primary OpenAI API key for general use.
OPENAI_API_KEY=sk-xxx

# Uncomment and set if you have a specific endpoint for llm.
# OPENAI_BASE_URL=https://api.openai.com/v1

# Uncomment and set if you want to use a specific model for llm model.
# OPENAI_MODEL=gpt-4o-mini


# Uncomment and set if you have a specific endpoint for embedding model.
# OPENAI_EMBEDDING_MODEL_BASE_URL=https://api.openai.com/v1

# Uncomment and set if you have a specific API key for embedding model.
# OPENAI_EMBEDDING_MODEL_API_KEY=sk-xxx

# Uncomment and set if you want to use a specific model for embedding model.
# OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Uncomment and set if you want to use a specific dimension for embedding model.
# OPENAI_EMBEDDING_MODEL_DIMS=1536


# ------ Database for Memory ------
# Uncomment and set if you want to use a specific database for memory.
# The default is SQLite, but you can change it to PostgreSQL or any other database supported by SQLAlchemy.
# DATABASE_URL=**************************************/openmemory
# DATABASE_URL=mysql+pymysql://username:password@ip:3306/openmemory